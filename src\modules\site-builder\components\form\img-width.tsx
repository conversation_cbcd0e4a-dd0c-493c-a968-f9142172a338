import React, { useEffect } from "react"
import { Input, Label, Slider } from "@/components/ui"
import { useWebsiteStore } from "@/store/website-store"

const MAX_VALUE = 1000

interface IProps {
  value?: number
  onChange?: (scaling: number) => void
}

const ImgWidthForm = ({ value = 0, onChange }: IProps) => {
  const websiteState = useWebsiteStore()
  const [scaling, setScaling] = React.useState(
    value > MAX_VALUE ? MAX_VALUE : value,
  )

  useEffect(() => {
    setScaling(value > MAX_VALUE ? MAX_VALUE : value)
  }, [value])

  const handleOnChange = (scaling: number) => {
    websiteState.onSetFormDirty(true)
    setScaling(scaling)
  }

  const handleRadiusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value)
    handleOnChange(value)
  }

  const handleBlur = () => {
    onChange && onChange(scaling)
    websiteState.onSetFormDirty(false)
  }

  return (
    <div className="flex items-center gap-4">
      <div className="flex flex-1 flex-col gap-3">
        <Label>Image Width</Label>
        <Slider
          min={0}
          max={MAX_VALUE}
          value={[scaling]}
          onValueChange={(value) => handleOnChange(value[0])}
          onValueCommit={handleBlur}
        />
      </div>
      <Input
        value={scaling}
        onChange={handleRadiusChange}
        onBlur={handleBlur}
        className="w-24"
        type="number"
      />
      <div className="w-5">px</div>
    </div>
  )
}

export default ImgWidthForm
