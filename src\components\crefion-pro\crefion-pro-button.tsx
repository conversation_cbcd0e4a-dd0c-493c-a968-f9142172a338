import { Crown } from "lucide-react"

import {
  But<PERSON>,
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  ScrollArea,
} from "@/components/ui"
import { ProPayment } from "@/modules/crefion-pro/templates"

type T_CrefionProButtonProps = {
  className?: string
}

const CrefionProButton = ({ className }: T_CrefionProButtonProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="gradient" className="w-full">
          <Crown className="h-5 w-5 text-white" />
          PRO
        </Button>
      </DialogTrigger>
      <DialogContent>
        <ScrollArea className="h-[80vh]">
          <DialogHeader>
            <DialogTitle>Upgrade to PRO</DialogTitle>
            <DialogDescription>
              Upgrade to PRO to get access to all features.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4">
            <ProPayment />
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Close</Button>
            </DialogClose>
            <Button type="submit">Upgrade</Button>
          </DialogFooter>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

export default CrefionProButton
