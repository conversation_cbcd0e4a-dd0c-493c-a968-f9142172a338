'use client';

import { isEmpty, startCase } from "lodash"

type TFormErrors = {
  errors: any
}

const FormErrors = ({ errors }: TFormErrors) => {

  if (isEmpty(errors)) return null

  return (
    <div className="bg-red-100 text-red-800 p-4 mb-4 rounded-lg">
      <h2 className="text-sm font-semibold mb-2">Form Errors</h2>
      <ul className="list-disc list-inside text-sm">
        {Object.entries(errors).map(
          ([key, error]: any, idx: number) => (
            <li key={idx}>{`${startCase(key)}: ${error?.message}`}</li>
          )
        )}
      </ul>
    </div>
  )
}

export default FormErrors