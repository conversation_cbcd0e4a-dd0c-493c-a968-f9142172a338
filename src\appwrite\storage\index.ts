// https://sys.techtown.app/v1/storage/buckets/66df3b29001d77a945a3/files/66e6a6820027fb44fc74/view?project=easy-tech&mode=admin

import { APP_WRITE, twicpics } from "@/config-global"
import { PLACEHOLDER } from "@/modules/site-builder/const"
const { endPointUrl, bucketId, projectId } = APP_WRITE || {}

export const getFileUrl = (fileId: string, userBucketId?: string) => {
  return `${endPointUrl}/storage/buckets/${userBucketId || bucketId}/files/${fileId}/view?project=${projectId}`
}

export const isAppwriteStorage = (url: string) => {
  return url.includes(endPointUrl)
}

export const getStoreIdFromUrl = (url: string) => {
  const parts = url.split("/")
  const fileId = parts[parts.indexOf("files") + 1]
  return fileId
}
export const getTwicPicsUrl = (fileId?: string | null): string | null => {
  if (!fileId) return PLACEHOLDER

  const appwriteStorage = `${endPointUrl}/storage/buckets`
  const twicPicsDomain = twicpics.domain

  return fileId.replace(appwriteStorage, twicPicsDomain)
}
